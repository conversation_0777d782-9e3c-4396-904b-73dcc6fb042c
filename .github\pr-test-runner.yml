name: PR Test Runner

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [ main, master ]

jobs:
  test:
    name: Run tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      
      - name: Install dependencies
        run: bun install
      
      - name: Run tests
        run: bun test
      
      # Optional: Add a Vite build step if needed
      - name: Build with Vite
        run: bun run build
      
      - name: Add test result comment to PR
        if: always()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const jobStatus = '${{ job.status }}';
            let message = '';
            
            if (jobStatus === 'success') {
              message = '✅ All tests passed! This PR is ready for review.';
            } else {
              message = '❌ Tests failed. Please review the test results before merging.';
            }
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: message
            });
