{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "esModuleInterop": true, "strict": true, "outDir": "dist", "rootDir": "src", "declaration": true, "sourceMap": true, "types": ["bun-types", "node"], "allowImportingTsExtensions": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}