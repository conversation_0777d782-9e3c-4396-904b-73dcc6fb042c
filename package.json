{"name": "confluence-mcp", "version": "1.0.0", "description": "Confluence MCP Server", "module": "index.ts", "type": "module", "scripts": {"build-linux": "vite build && chmod +x dist/index.js", "build": "vite build", "start": "bun dist/index.js", "dev": "bun --watch src/index.ts", "test": "bun test", "test:watch": "bun test --watch", "prepare": "husky"}, "keywords": ["mcp", "confluence", "typescript", "bun"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@types/bun": "^1.2.13", "@types/node": "^22.15.19", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vitest": "^3.1.4"}, "peerDependencies": {"typescript": "^5.8.3"}, "dependencies": {"sanitize-html": "^2.17.0", "@modelcontextprotocol/sdk": "^1.11.4"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": "prettier --write"}}